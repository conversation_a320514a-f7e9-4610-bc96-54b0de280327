"""
Contact dashboard generic serializer
"""

from rest_framework import serializers


class CrmDashboardMetricSerializer(serializers.Serializer):
    """
    Serializer for metrics with comparison data
    """

    value = serializers.IntegerField()
    percentage = serializers.CharField()
    tendency = serializers.CharField()


class CrmDashboardContactStatsSerializer(serializers.Serializer):
    """
    Serializer for contact dashboard statistics
    """

    total_contacts = serializers.IntegerField()
    new_this_month = CrmDashboardMetricSerializer()
    active_percentage = serializers.FloatField()
    conversion_rate = CrmDashboardMetricSerializer()
    contacts_by_status = serializers.ListField(
        child=serializers.DictField(child=serializers.CharField())
    )


class CrmDashboardContactsByMonthSerializer(serializers.Serializer):
    """
    Serializer for contacts by month data
    """

    name = serializers.CharField()
    contacts = serializers.IntegerField()
    totalAccumulated = serializers.IntegerField()


class CrmDashboardNameValueSerializer(serializers.Serializer):
    """
    Generic serializer for name-value pairs used in charts
    """

    name = serializers.CharField()
    value = serializers.IntegerField()


class CrmDashboardCountryContactsSerializer(serializers.Serializer):
    """
    Serializer for contacts by country (TreeMap data)
    """

    country = serializers.CharField()
    contacts = serializers.IntegerField()


class CrmDashboardContactsSerializer(serializers.Serializer):
    """
    Serializer for all dashboard data
    """

    stats = CrmDashboardContactStatsSerializer()
    contacts_by_month = CrmDashboardContactsByMonthSerializer(many=True)
    contacts_by_country = CrmDashboardCountryContactsSerializer(many=True)
    contacts_by_occupation = CrmDashboardNameValueSerializer(many=True)
    students_by_major = CrmDashboardNameValueSerializer(many=True)
    students_by_term = CrmDashboardNameValueSerializer(many=True)
    students_by_university = CrmDashboardNameValueSerializer(many=True)
